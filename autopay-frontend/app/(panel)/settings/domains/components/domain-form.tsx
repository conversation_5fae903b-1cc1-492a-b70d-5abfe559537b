'use client'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { But<PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useAuth } from '@/lib/hooks/useAuth'
import { Domain } from '@/lib/types/domain'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AlertCircle } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

const domainSchema = z.object({
  frontend_hostname: z
    .string()
    .optional()
    .refine((val) => {
      if (!val) return true
      return /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(val)
    }, 'Định dạng frontend hostname không hợp lệ'),
  backend_hostname: z
    .string()
    .optional()
    .refine((val) => {
      if (!val) return true
      return /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(val)
    }, 'Định dạng backend hostname không hợp lệ'),
})

type DomainFormData = z.infer<typeof domainSchema>

interface DomainFormProps {
  domain?: Domain | null
  onClose: () => void
}

export function DomainForm({ domain, onClose }: DomainFormProps) {
  const { user, loading: authLoading } = useAuth()
  const queryClient = useQueryClient()
  const [error, setError] = useState<string | null>(null)
  const [validationErrors, setValidationErrors] = useState<string[]>([])

  // Create domain mutation
  const { isPending: isCreating, mutate: createDomain } = useMutation({
    mutationKey: ['createDomain'],
    retry: false, // Disable retry to prevent unwanted requests
    mutationFn: (data: DomainFormData) => {
      // console.log('🚀 Creating domain with data:', data)
      const orgId = user?.current_organization?.id
      if (!orgId) {
        throw new Error('Không tìm thấy thông tin tổ chức')
      }
      return queryFetchHelper(`/${orgId}/domains`, {
        method: 'POST',
        body: JSON.stringify(data),
      })
    },
    onSuccess: (response) => {
      if (response.success) {
        toast.success(response.message || 'Domain đã được tạo thành công')
        queryClient.invalidateQueries({ queryKey: ['domain'] })
        onClose()
      } else {
        if (response.errors) {
          setValidationErrors(Array.isArray(response.errors) ? response.errors : [response.message])
        } else {
          setError(response.message || 'Có lỗi xảy ra khi tạo domain')
        }
      }
    },
    onError: (error: any) => {
      if (error.errors) {
        setValidationErrors(Array.isArray(error.errors) ? error.errors : [error.message])
      } else {
        setError(error.message || 'Có lỗi xảy ra khi tạo domain')
      }
    },
  })

  // Update domain mutation
  const { isPending: isUpdating, mutate: updateDomain } = useMutation({
    mutationKey: ['updateDomain', domain?.id],
    retry: false, // Disable retry to prevent unwanted requests
    mutationFn: (data: DomainFormData) => {
      // console.log('🔄 Updating domain with data:', data, 'domain ID:', domain?.id)
      const orgId = user?.current_organization?.id
      if (!orgId) {
        throw new Error('Không tìm thấy thông tin tổ chức')
      }
      if (!domain?.id) {
        throw new Error('Không tìm thấy ID domain')
      }
      return queryFetchHelper(`/${orgId}/domains/${domain.id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      })
    },
    onSuccess: (response) => {
      if (response.success) {
        toast.success(response.message || 'Domain đã được cập nhật thành công')
        queryClient.invalidateQueries({ queryKey: ['domain'] })
        onClose()
      } else {
        if (response.errors) {
          setValidationErrors(Array.isArray(response.errors) ? response.errors : [response.message])
        } else {
          setError(response.message || 'Có lỗi xảy ra khi cập nhật domain')
        }
      }
    },
    onError: (error: any) => {
      if (error.errors) {
        setValidationErrors(Array.isArray(error.errors) ? error.errors : [error.message])
      } else {
        setError(error.message || 'Có lỗi xảy ra khi cập nhật domain')
      }
    },
  })

  const form = useForm<DomainFormData>({
    resolver: zodResolver(domainSchema),
    defaultValues: {
      frontend_hostname: domain?.frontend_hostname || '',
      backend_hostname: domain?.backend_hostname || '',
    },
  })

  // Reset form when domain prop changes
  useEffect(() => {
    form.reset({
      frontend_hostname: domain?.frontend_hostname || '',
      backend_hostname: domain?.backend_hostname || '',
    })
  }, [domain, form])

  const isEditing = !!domain
  const isLoading = isCreating || isUpdating || authLoading

  const onSubmit = (data: DomainFormData) => {
    // console.log('📝 onSubmit called with data:', data, 'isEditing:', isEditing)
    setError(null)
    setValidationErrors([])

    // Check if auth is still loading
    if (authLoading) {
      setError('Đang tải thông tin người dùng. Vui lòng đợi...')
      return
    }

    // Get current organization ID from user
    const orgId = user?.current_organization?.id

    if (!orgId) {
      setError('Không tìm thấy thông tin tổ chức. Vui lòng đăng nhập lại.')
      return
    }

    if (isEditing) {
      updateDomain(data)
    } else {
      createDomain(data)
    }
  }

  return (
    <Dialog
      open={true}
      onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Chỉnh sửa Domain' : 'Thêm Domain mới'}</DialogTitle>
          <DialogDescription>
            {isEditing ? 'Cập nhật thông tin domain của bạn' : 'Thêm domain mới cho tổ chức của bạn'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4">
            {authLoading && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>Đang tải thông tin người dùng...</AlertDescription>
              </Alert>
            )}

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {validationErrors.length > 0 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <ul className="list-inside list-disc space-y-1">
                    {validationErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            <FormField
              control={form.control}
              name="frontend_hostname"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Frontend Domain</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="app.example.com hoặc frontend.autopay.vn"
                      disabled={authLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>Domain cho ứng dụng frontend (customer interface)</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="backend_hostname"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Backend Domain</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="api.example.com hoặc backend.autopay.vn"
                      disabled={authLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>Domain cho API backend (admin/staff interface)</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}>
                Hủy
              </Button>
              <Button
                type="submit"
                disabled={isLoading}>
                {isLoading ? 'Đang xử lý...' : isEditing ? 'Cập nhật' : 'Thêm Domain'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
