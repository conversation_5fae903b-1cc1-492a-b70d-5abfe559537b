'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import FormSkeleton from '@/components/display/FormSkeleton'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import useAuth from '@/lib/hooks/useAuth'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { toast } from 'sonner'

const advancedSettingsSchema = z.object({
  seo: z.object({
    title: z.string().optional(),
    description: z.string().optional(),
    keywords: z.string().optional(),
    og_image: z.string().url().optional().or(z.literal('')),
  }),
  contact: z.record(z.any()).optional(),
  custom: z.record(z.any()).optional(),
})

type AdvancedSettingsValues = z.infer<typeof advancedSettingsSchema>

const defaultValues: Partial<AdvancedSettingsValues> = {
  seo: {
    title: '',
    description: '',
    keywords: '',
    og_image: '',
  },
  contact: {},
  custom: {},
}

export function AdvancedSettingsForm() {
  const { user, loading: authLoading } = useAuth()
  const queryClient = useQueryClient()

  // Fetch current domain data
  const { data: domain, isLoading: isDomainLoading } = useQuery({
    queryKey: ['domain', user?.current_organization?.id],
    queryFn: async () => {
      if (!user?.current_organization?.id) {
        throw new Error('Không tìm thấy thông tin tổ chức')
      }
      const response = await queryFetchHelper(`/${user.current_organization.id}/domains`)
      return response.data
    },
    enabled: !!user?.current_organization?.id && !authLoading,
  })

  const form = useForm<AdvancedSettingsValues>({
    resolver: zodResolver(advancedSettingsSchema),
    defaultValues,
    mode: 'onChange',
  })

  // Update form when domain data is loaded
  useEffect(() => {
    if (domain?.data) {
      const domainData = domain.data
      form.reset({
        seo: {
          title: domainData.seo?.title || '',
          description: domainData.seo?.description || '',
          keywords: domainData.seo?.keywords || '',
          og_image: domainData.seo?.og_image || '',
        },
        contact: domainData.contact || {},
        custom: domainData.custom || {},
      })
    }
  }, [domain, form])

  // Update domain mutation
  const updateDomainMutation = useMutation({
    mutationFn: async (data: AdvancedSettingsValues) => {
      if (!user?.current_organization?.id || !domain?.id) {
        throw new Error('Thiếu thông tin tổ chức hoặc domain')
      }

      const updateData = {
        data: {
          ...domain.data,
          seo: {
            ...domain.data?.seo,
            title: data.seo.title,
            description: data.seo.description,
            keywords: data.seo.keywords,
            og_image: data.seo.og_image,
          },
          contact: data.contact || domain.data?.contact || {},
          custom: data.custom || domain.data?.custom || {},
        },
      }

      const response = await queryFetchHelper(`/${user.current_organization.id}/domains/${domain.id}`, {
        method: 'PUT',
        body: JSON.stringify(updateData),
        headers: {
          'Content-Type': 'application/json',
        },
      })
      return response.data
    },
    onSuccess: (response) => {
      toast.success('Cập nhật thiết lập nâng cao thành công')

      // Update cache directly instead of invalidating to prevent data loss
      if (response) {
        queryClient.setQueryData(['domain', user?.current_organization?.id], response)
      } else {
        // Fallback to invalidation if no response data
        queryClient.invalidateQueries({ queryKey: ['domain', user?.current_organization?.id] })
      }
    },
    onError: (error: any) => {
      console.error('❌ AdvancedSettings: Update error:', error)
      const errorMessage = error?.message || 'Có lỗi xảy ra khi cập nhật thiết lập nâng cao'
      toast.error(errorMessage)
    },
  })

  async function onSubmit(data: AdvancedSettingsValues) {
    console.log('🚀 AdvancedSettings: Submitting data:', data)
    updateDomainMutation.mutate(data)
  }

  // Show loading skeleton while fetching domain data
  if (authLoading || isDomainLoading) {
    return (
      <div className="space-y-6">
        <FormSkeleton
          titleWidth="w-32"
          descriptionWidth="w-48"
          className="mb-6"
        />
        <FormSkeleton
          titleWidth="w-40"
          descriptionWidth="w-56"
          className="mb-6"
        />
        <FormSkeleton
          titleWidth="w-36"
          descriptionWidth="w-52"
          buttonWidth="w-32"
        />
      </div>
    )
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">SEO</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="seo.title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Meta Title</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="AutoPAY - Thanh toán tự động"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="seo.description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Meta Description</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Mô tả cho SEO..."
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="seo.keywords"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Meta Keywords</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="thanh toán, tự động, autopay"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="seo.og_image"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Open Graph Image URL</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="https://example.com/og-image.jpg"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Button
          type="submit"
          size="sm"
          disabled={updateDomainMutation.isPending || isDomainLoading || authLoading}>
          {updateDomainMutation.isPending ? 'Đang cập nhật...' : 'Cập nhật thiết lập'}
        </Button>
      </form>
    </Form>
  )
}
