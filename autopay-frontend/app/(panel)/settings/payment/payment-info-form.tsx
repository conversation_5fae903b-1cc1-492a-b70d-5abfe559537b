'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import FormSkeleton from '@/components/display/FormSkeleton'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import useAuth from '@/lib/hooks/useAuth'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { toast } from 'sonner'

const paymentInfoSchema = z.object({
  bank_name: z.string().optional(),
  account_number: z.string().optional(),
  account_holder: z.string().optional(),
  branch: z.string().optional(),
  swift_code: z.string().optional(),
  tax_code: z.string().optional(),
  company_address: z.string().optional(),
})

type PaymentInfoValues = z.infer<typeof paymentInfoSchema>

const defaultValues: Partial<PaymentInfoValues> = {
  bank_name: '',
  account_number: '',
  account_holder: '',
  branch: '',
  swift_code: '',
  tax_code: '',
  company_address: '',
}

export function PaymentInfoForm() {
  const { user, loading: authLoading } = useAuth()
  const queryClient = useQueryClient()

  // Fetch current domain data
  const { data: domain, isLoading: isDomainLoading } = useQuery({
    queryKey: ['domain', user?.current_organization?.id],
    queryFn: async () => {
      if (!user?.current_organization?.id) {
        throw new Error('Không tìm thấy thông tin tổ chức')
      }
      const response = await queryFetchHelper(`/${user.current_organization.id}/domains`)
      return response.data
    },
    enabled: !!user?.current_organization?.id && !authLoading,
  })

  const form = useForm<PaymentInfoValues>({
    resolver: zodResolver(paymentInfoSchema),
    defaultValues,
    mode: 'onChange',
  })

  // Update form when domain data is loaded
  useEffect(() => {
    if (domain?.data?.contact) {
      const contactData = domain.data.contact
      form.reset({
        bank_name: contactData.bank_name || '',
        account_number: contactData.account_number || '',
        account_holder: contactData.account_holder || '',
        branch: contactData.branch || '',
        swift_code: contactData.swift_code || '',
        tax_code: contactData.tax_code || '',
        company_address: contactData.company_address || '',
      })
    }
  }, [domain, form])

  // Update domain mutation
  const updateDomainMutation = useMutation({
    mutationFn: async (data: PaymentInfoValues) => {
      if (!user?.current_organization?.id || !domain?.id) {
        throw new Error('Thiếu thông tin tổ chức hoặc domain')
      }

      const updateData = {
        data: {
          ...domain.data,
          contact: {
            ...domain.data?.contact,
            bank_name: data.bank_name,
            account_number: data.account_number,
            account_holder: data.account_holder,
            branch: data.branch,
            swift_code: data.swift_code,
            tax_code: data.tax_code,
            company_address: data.company_address,
          },
        },
      }

      const response = await queryFetchHelper(`/${user.current_organization.id}/domains/${domain.id}`, {
        method: 'PUT',
        body: JSON.stringify(updateData),
        headers: {
          'Content-Type': 'application/json',
        },
      })
      return response.data
    },
    onSuccess: (response) => {
      toast.success('Cập nhật thông tin thanh toán thành công')

      // Update cache directly instead of invalidating to prevent data loss
      if (response) {
        queryClient.setQueryData(['domain', user?.current_organization?.id], { data: response })
      } else {
        // Fallback to invalidation if no response data
        queryClient.invalidateQueries({ queryKey: ['domain', user?.current_organization?.id] })
      }
    },
    onError: (error: any) => {
      const errorMessage = error?.message || 'Có lỗi xảy ra khi cập nhật thông tin thanh toán'
      toast.error(errorMessage)
    },
  })

  async function onSubmit(data: PaymentInfoValues) {
    updateDomainMutation.mutate(data)
  }

  // Show loading skeleton while fetching domain data
  if (authLoading || isDomainLoading) {
    return (
      <div className="space-y-6">
        <FormSkeleton
          titleWidth="w-40"
          descriptionWidth="w-56"
          className="mb-6"
        />
        <FormSkeleton
          titleWidth="w-32"
          descriptionWidth="w-48"
          className="mb-6"
        />
        <FormSkeleton
          titleWidth="w-24"
          descriptionWidth="w-40"
          buttonWidth="w-32"
        />
      </div>
    )
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6">
        <Card>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="bank_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên ngân hàng</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Vietcombank"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="account_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Số tài khoản</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="**********"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="account_holder"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Chủ tài khoản</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="CÔNG TY TNHH ABC"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="branch"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Chi nhánh</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Chi nhánh Hà Nội"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="swift_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mã SWIFT</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="BFTVVNVX"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="tax_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mã số thuế</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="0123456789"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="company_address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Địa chỉ công ty</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Số 123, Đường ABC, Phường XYZ, Quận DEF, Thành phố Hà Nội"
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Button
          type="submit"
          size="sm"
          disabled={updateDomainMutation.isPending || isDomainLoading || authLoading}>
          {updateDomainMutation.isPending ? 'Đang cập nhật...' : 'Cập nhật thông tin'}
        </Button>
      </form>
    </Form>
  )
}
